import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/produit.dart';

class ProduitService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Créer un nouveau produit
  Future<int> creerProduit(Produit produit) async {
    final db = await _databaseHelper.database;
    return await db.insert('produits', produit.toMap());
  }

  // Obtenir tous les produits
  Future<List<Produit>> obtenirTousLesProduits() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'produits',
      orderBy: 'nom ASC',
    );

    return List.generate(maps.length, (i) {
      return Produit.fromMap(maps[i]);
    });
  }

  // Obtenir les produits actifs seulement
  Future<List<Produit>> obtenirProduitsActifs() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'produits',
      where: 'actif = ?',
      whereArgs: [1],
      orderBy: 'nom ASC',
    );

    return List.generate(maps.length, (i) {
      return Produit.fromMap(maps[i]);
    });
  }

  // Obtenir un produit par ID
  Future<Produit?> obtenirProduitParId(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'produits',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Produit.fromMap(maps.first);
    }
    return null;
  }

  // Rechercher des produits par nom ou catégorie
  Future<List<Produit>> rechercherProduits(String terme) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'produits',
      where: 'nom LIKE ? OR description LIKE ? OR categorie LIKE ?',
      whereArgs: ['%$terme%', '%$terme%', '%$terme%'],
      orderBy: 'nom ASC',
    );

    return List.generate(maps.length, (i) {
      return Produit.fromMap(maps[i]);
    });
  }

  // Obtenir les produits par catégorie
  Future<List<Produit>> obtenirProduitsParCategorie(String categorie) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'produits',
      where: 'categorie = ? AND actif = ?',
      whereArgs: [categorie, 1],
      orderBy: 'nom ASC',
    );

    return List.generate(maps.length, (i) {
      return Produit.fromMap(maps[i]);
    });
  }

  // Obtenir toutes les catégories
  Future<List<String>> obtenirCategories() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      'SELECT DISTINCT categorie FROM produits WHERE actif = 1 ORDER BY categorie ASC',
    );

    return maps.map((map) => map['categorie'] as String).toList();
  }

  // Mettre à jour un produit
  Future<int> mettreAJourProduit(Produit produit) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'produits',
      produit.toMap(),
      where: 'id = ?',
      whereArgs: [produit.id],
    );
  }

  // Mettre à jour le stock d'un produit
  Future<int> mettreAJourStock(int produitId, int nouveauStock) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'produits',
      {'stock': nouveauStock},
      where: 'id = ?',
      whereArgs: [produitId],
    );
  }

  // Réduire le stock (lors d'une commande)
  Future<bool> reduireStock(int produitId, int quantite) async {
    final db = await _databaseHelper.database;
    
    // Obtenir le stock actuel
    final produit = await obtenirProduitParId(produitId);
    if (produit == null || produit.stock < quantite) {
      return false; // Stock insuffisant
    }
    
    final nouveauStock = produit.stock - quantite;
    await mettreAJourStock(produitId, nouveauStock);
    return true;
  }

  // Supprimer un produit (désactiver)
  Future<int> supprimerProduit(int id) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'produits',
      {'actif': 0},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Obtenir les produits en rupture de stock
  Future<List<Produit>> obtenirProduitsRuptureStock() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'produits',
      where: 'stock <= 0 AND actif = ?',
      whereArgs: [1],
      orderBy: 'nom ASC',
    );

    return List.generate(maps.length, (i) {
      return Produit.fromMap(maps[i]);
    });
  }

  // Obtenir les produits avec stock faible (moins de 10)
  Future<List<Produit>> obtenirProduitsStockFaible() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'produits',
      where: 'stock > 0 AND stock < 10 AND actif = ?',
      whereArgs: [1],
      orderBy: 'stock ASC',
    );

    return List.generate(maps.length, (i) {
      return Produit.fromMap(maps[i]);
    });
  }

  // Obtenir le nombre total de produits actifs
  Future<int> obtenirNombreProduits() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM produits WHERE actif = 1');
    return result.first['count'] as int;
  }
}
