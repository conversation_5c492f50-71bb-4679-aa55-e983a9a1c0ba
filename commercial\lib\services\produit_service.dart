import '../models/produit.dart';
import 'mock_data_service.dart';

class ProduitService {
  final MockDataService _mockService = MockDataService();

  // Créer un nouveau produit
  Future<int> creerProduit(Produit produit) async {
    await _mockService.initialize();
    return _mockService.addProduit(produit);
  }

  // Obtenir tous les produits
  Future<List<Produit>> obtenirTousLesProduits() async {
    await _mockService.initialize();
    final produits = _mockService.produits;
    produits.sort((a, b) => a.nom.compareTo(b.nom));
    return produits;
  }

  // Obtenir les produits actifs seulement
  Future<List<Produit>> obtenirProduitsActifs() async {
    await _mockService.initialize();
    final produits = _mockService.produits.where((p) => p.actif).toList();
    produits.sort((a, b) => a.nom.compareTo(b.nom));
    return produits;
  }

  // Obtenir un produit par ID
  Future<Produit?> obtenirProduitParId(int id) async {
    await _mockService.initialize();
    return _mockService.getProduitById(id);
  }

  // Rechercher des produits par nom ou catégorie
  Future<List<Produit>> rechercherProduits(String terme) async {
    await _mockService.initialize();
    final produits = _mockService.searchProduits(terme);
    produits.sort((a, b) => a.nom.compareTo(b.nom));
    return produits;
  }

  // Obtenir les produits par catégorie
  Future<List<Produit>> obtenirProduitsParCategorie(String categorie) async {
    await _mockService.initialize();
    final produits = _mockService.getProduitsByCategory(categorie);
    produits.sort((a, b) => a.nom.compareTo(b.nom));
    return produits;
  }

  // Obtenir toutes les catégories
  Future<List<String>> obtenirCategories() async {
    await _mockService.initialize();
    return _mockService.categories;
  }

  // Mettre à jour un produit
  Future<int> mettreAJourProduit(Produit produit) async {
    await _mockService.initialize();
    return _mockService.updateProduit(produit) ? 1 : 0;
  }

  // Mettre à jour le stock d'un produit
  Future<int> mettreAJourStock(int produitId, int nouveauStock) async {
    await _mockService.initialize();
    final produit = _mockService.getProduitById(produitId);
    if (produit != null) {
      final produitMisAJour = produit.copyWith(stock: nouveauStock);
      return _mockService.updateProduit(produitMisAJour) ? 1 : 0;
    }
    return 0;
  }

  // Réduire le stock (lors d'une commande)
  Future<bool> reduireStock(int produitId, int quantite) async {
    await _mockService.initialize();
    final produit = _mockService.getProduitById(produitId);
    if (produit == null || produit.stock < quantite) {
      return false; // Stock insuffisant
    }
    
    final nouveauStock = produit.stock - quantite;
    final produitMisAJour = produit.copyWith(stock: nouveauStock);
    return _mockService.updateProduit(produitMisAJour);
  }

  // Supprimer un produit (désactiver)
  Future<int> supprimerProduit(int id) async {
    await _mockService.initialize();
    return _mockService.deleteProduit(id) ? 1 : 0;
  }

  // Obtenir les produits en rupture de stock
  Future<List<Produit>> obtenirProduitsRuptureStock() async {
    await _mockService.initialize();
    return _mockService.produits
        .where((p) => p.actif && p.stock <= 0)
        .toList()
      ..sort((a, b) => a.nom.compareTo(b.nom));
  }

  // Obtenir les produits avec stock faible (moins de 10)
  Future<List<Produit>> obtenirProduitsStockFaible() async {
    await _mockService.initialize();
    return _mockService.produits
        .where((p) => p.actif && p.stock > 0 && p.stock < 10)
        .toList()
      ..sort((a, b) => a.stock.compareTo(b.stock));
  }

  // Obtenir le nombre total de produits actifs
  Future<int> obtenirNombreProduits() async {
    await _mockService.initialize();
    return _mockService.produits.where((p) => p.actif).length;
  }
}
