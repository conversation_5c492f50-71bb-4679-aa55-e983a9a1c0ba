import 'package:flutter/foundation.dart';
import '../models/commande.dart';
import '../models/commande_item.dart';
import '../models/produit.dart';
import '../services/commande_service.dart';

class CommandeProvider with ChangeNotifier {
  final CommandeService _commandeService = CommandeService();
  
  List<Commande> _commandes = [];
  List<CommandeItem> _panier = [];
  bool _isLoading = false;
  String? _error;

  List<Commande> get commandes => _commandes;
  List<CommandeItem> get panier => _panier;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Calculer le total du panier
  double get totalPanier => _panier.fold(0.0, (total, item) => total + item.sousTotal);
  
  // Nombre d'articles dans le panier
  int get nombreArticlesPanier => _panier.fold(0, (total, item) => total + item.quantite);

  // Charger toutes les commandes
  Future<void> chargerCommandes() async {
    _setLoading(true);
    try {
      _commandes = await _commandeService.obtenirToutesLesCommandes();
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du chargement des commandes: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Charger les commandes d'un client
  Future<void> chargerCommandesClient(int clientId) async {
    _setLoading(true);
    try {
      _commandes = await _commandeService.obtenirCommandesClient(clientId);
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du chargement des commandes du client: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Ajouter un produit au panier
  void ajouterAuPanier(Produit produit, int quantite) {
    // Vérifier si le produit est déjà dans le panier
    final index = _panier.indexWhere((item) => item.produitId == produit.id);
    
    if (index != -1) {
      // Mettre à jour la quantité
      final item = _panier[index];
      final nouvelleQuantite = item.quantite + quantite;
      _panier[index] = CommandeItem.fromProduit(
        commandeId: 0, // Sera défini lors de la création de la commande
        produitId: produit.id!,
        nomProduit: produit.nom,
        prixUnitaire: produit.prix,
        quantite: nouvelleQuantite,
      );
    } else {
      // Ajouter un nouvel item
      _panier.add(CommandeItem.fromProduit(
        commandeId: 0, // Sera défini lors de la création de la commande
        produitId: produit.id!,
        nomProduit: produit.nom,
        prixUnitaire: produit.prix,
        quantite: quantite,
      ));
    }
    
    notifyListeners();
  }

  // Modifier la quantité d'un item dans le panier
  void modifierQuantitePanier(int produitId, int nouvelleQuantite) {
    if (nouvelleQuantite <= 0) {
      retirerDuPanier(produitId);
      return;
    }
    
    final index = _panier.indexWhere((item) => item.produitId == produitId);
    if (index != -1) {
      final item = _panier[index];
      _panier[index] = CommandeItem.fromProduit(
        commandeId: 0,
        produitId: item.produitId,
        nomProduit: item.nomProduit,
        prixUnitaire: item.prixUnitaire,
        quantite: nouvelleQuantite,
      );
      notifyListeners();
    }
  }

  // Retirer un produit du panier
  void retirerDuPanier(int produitId) {
    _panier.removeWhere((item) => item.produitId == produitId);
    notifyListeners();
  }

  // Vider le panier
  void viderPanier() {
    _panier.clear();
    notifyListeners();
  }

  // Créer une commande à partir du panier
  Future<bool> creerCommandeDepuisPanier(int clientId, {String? notes}) async {
    if (_panier.isEmpty) {
      _error = 'Le panier est vide';
      notifyListeners();
      return false;
    }

    try {
      final commande = Commande(
        clientId: clientId,
        dateCommande: DateTime.now(),
        montantTotal: totalPanier,
        notes: notes,
        items: _panier,
      );

      final commandeId = await _commandeService.creerCommande(commande);
      
      // Ajouter la nouvelle commande à la liste
      final nouvelleCommande = commande.copyWith(id: commandeId);
      _commandes.insert(0, nouvelleCommande);
      
      // Vider le panier
      viderPanier();
      
      _error = null;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la création de la commande: $e';
      notifyListeners();
      return false;
    }
  }

  // Mettre à jour le statut d'une commande
  Future<bool> mettreAJourStatut(int commandeId, StatutCommande nouveauStatut) async {
    try {
      await _commandeService.mettreAJourStatut(commandeId, nouveauStatut);
      
      final index = _commandes.indexWhere((c) => c.id == commandeId);
      if (index != -1) {
        _commandes[index] = _commandes[index].copyWith(statut: nouveauStatut);
      }
      
      _error = null;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la mise à jour du statut: $e';
      notifyListeners();
      return false;
    }
  }

  // Annuler une commande
  Future<bool> annulerCommande(int commandeId) async {
    try {
      final success = await _commandeService.annulerCommande(commandeId);
      if (success) {
        final index = _commandes.indexWhere((c) => c.id == commandeId);
        if (index != -1) {
          _commandes[index] = _commandes[index].copyWith(statut: StatutCommande.annulee);
        }
        _error = null;
        notifyListeners();
      }
      return success;
    } catch (e) {
      _error = 'Erreur lors de l\'annulation de la commande: $e';
      notifyListeners();
      return false;
    }
  }

  // Filtrer les commandes par statut
  Future<void> filtrerParStatut(StatutCommande? statut) async {
    _setLoading(true);
    try {
      if (statut == null) {
        _commandes = await _commandeService.obtenirToutesLesCommandes();
      } else {
        _commandes = await _commandeService.obtenirCommandesParStatut(statut);
      }
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du filtrage: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Obtenir une commande par ID
  Commande? obtenirCommandeParId(int id) {
    try {
      return _commandes.firstWhere((commande) => commande.id == id);
    } catch (e) {
      return null;
    }
  }

  // Effacer l'erreur
  void effacerErreur() {
    _error = null;
    notifyListeners();
  }

  // Méthode privée pour gérer le loading
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Obtenir les statistiques des commandes
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      final nombreTotal = await _commandeService.obtenirNombreCommandes();
      final chiffreAffaires = await _commandeService.obtenirChiffreAffaires();
      final commandesRecentes = await _commandeService.obtenirCommandesRecentes();
      
      return {
        'nombreTotal': nombreTotal,
        'chiffreAffaires': chiffreAffaires,
        'nombreRecentes': commandesRecentes.length,
        'commandesRecentes': commandesRecentes,
      };
    } catch (e) {
      return {
        'nombreTotal': 0,
        'chiffreAffaires': 0.0,
        'nombreRecentes': 0,
        'commandesRecentes': <Commande>[],
      };
    }
  }
}
