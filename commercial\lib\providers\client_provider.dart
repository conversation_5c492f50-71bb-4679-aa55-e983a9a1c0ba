import 'package:flutter/foundation.dart';
import '../models/client.dart';
import '../services/client_service.dart';

class ClientProvider with ChangeNotifier {
  final ClientService _clientService = ClientService();
  
  List<Client> _clients = [];
  bool _isLoading = false;
  String? _error;

  List<Client> get clients => _clients;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Charger tous les clients
  Future<void> chargerClients() async {
    _setLoading(true);
    try {
      _clients = await _clientService.obtenirTousLesClients();
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du chargement des clients: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Ajouter un nouveau client
  Future<bool> ajouterClient(Client client) async {
    try {
      // Vérifier si l'email existe déjà
      final emailExiste = await _clientService.emailExiste(client.email);
      if (emailExiste) {
        _error = 'Un client avec cet email existe déjà';
        notifyListeners();
        return false;
      }

      final id = await _clientService.creerClient(client);
      final nouveauClient = client.copyWith(id: id);
      _clients.add(nouveauClient);
      _clients.sort((a, b) => a.nom.compareTo(b.nom));
      _error = null;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de l\'ajout du client: $e';
      notifyListeners();
      return false;
    }
  }

  // Modifier un client
  Future<bool> modifierClient(Client client) async {
    try {
      // Vérifier si l'email existe déjà (excluant le client actuel)
      final emailExiste = await _clientService.emailExiste(
        client.email,
        excludeId: client.id,
      );
      if (emailExiste) {
        _error = 'Un autre client avec cet email existe déjà';
        notifyListeners();
        return false;
      }

      await _clientService.mettreAJourClient(client);
      final index = _clients.indexWhere((c) => c.id == client.id);
      if (index != -1) {
        _clients[index] = client;
        _clients.sort((a, b) => a.nom.compareTo(b.nom));
      }
      _error = null;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la modification du client: $e';
      notifyListeners();
      return false;
    }
  }

  // Supprimer un client
  Future<bool> supprimerClient(int clientId) async {
    try {
      await _clientService.supprimerClient(clientId);
      _clients.removeWhere((client) => client.id == clientId);
      _error = null;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la suppression du client: $e';
      notifyListeners();
      return false;
    }
  }

  // Rechercher des clients
  Future<void> rechercherClients(String terme) async {
    if (terme.isEmpty) {
      await chargerClients();
      return;
    }

    _setLoading(true);
    try {
      _clients = await _clientService.rechercherClients(terme);
      _error = null;
    } catch (e) {
      _error = 'Erreur lors de la recherche: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Obtenir un client par ID
  Client? obtenirClientParId(int id) {
    try {
      return _clients.firstWhere((client) => client.id == id);
    } catch (e) {
      return null;
    }
  }

  // Effacer l'erreur
  void effacerErreur() {
    _error = null;
    notifyListeners();
  }

  // Méthode privée pour gérer le loading
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Obtenir les statistiques des clients
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      final nombreTotal = await _clientService.obtenirNombreClients();
      final clientsRecents = await _clientService.obtenirClientsRecents();
      
      return {
        'nombreTotal': nombreTotal,
        'nombreRecents': clientsRecents.length,
        'clientsRecents': clientsRecents,
      };
    } catch (e) {
      return {
        'nombreTotal': 0,
        'nombreRecents': 0,
        'clientsRecents': <Client>[],
      };
    }
  }
}
