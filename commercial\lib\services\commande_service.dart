import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/commande.dart';
import '../models/commande_item.dart';
import 'produit_service.dart';

class CommandeService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final ProduitService _produitService = ProduitService();

  // Créer une nouvelle commande avec ses items
  Future<int> creerCommande(Commande commande) async {
    final db = await _databaseHelper.database;

    return await db.transaction((txn) async {
      // Insérer la commande
      final commandeId = await txn.insert('commandes', commande.toMap());

      // Insérer les items de la commande
      for (final item in commande.items) {
        final itemWithCommandeId = item.copyWith(commandeId: commandeId);
        await txn.insert('commande_items', itemWithCommandeId.toMap());

        // Réduire le stock du produit
        await _reduireStockDansTransaction(txn, item.produitId, item.quantite);
      }

      return commandeId;
    });
  }

  // Réduire le stock dans une transaction
  Future<void> _reduireStockDansTransaction(
    Transaction txn,
    int produitId,
    int quantite,
  ) async {
    final List<Map<String, dynamic>> produitMaps = await txn.query(
      'produits',
      where: 'id = ?',
      whereArgs: [produitId],
    );

    if (produitMaps.isNotEmpty) {
      final stockActuel = produitMaps.first['stock'] as int;
      final nouveauStock = stockActuel - quantite;

      await txn.update(
        'produits',
        {'stock': nouveauStock},
        where: 'id = ?',
        whereArgs: [produitId],
      );
    }
  }

  // Obtenir toutes les commandes
  Future<List<Commande>> obtenirToutesLesCommandes() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'commandes',
      orderBy: 'dateCommande DESC',
    );

    List<Commande> commandes = [];
    for (final map in maps) {
      final items = await obtenirItemsCommande(map['id']);
      commandes.add(Commande.fromMap(map, items: items));
    }

    return commandes;
  }

  // Obtenir une commande par ID
  Future<Commande?> obtenirCommandeParId(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'commandes',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      final items = await obtenirItemsCommande(id);
      return Commande.fromMap(maps.first, items: items);
    }
    return null;
  }

  // Obtenir les commandes d'un client
  Future<List<Commande>> obtenirCommandesClient(int clientId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'commandes',
      where: 'clientId = ?',
      whereArgs: [clientId],
      orderBy: 'dateCommande DESC',
    );

    List<Commande> commandes = [];
    for (final map in maps) {
      final items = await obtenirItemsCommande(map['id']);
      commandes.add(Commande.fromMap(map, items: items));
    }

    return commandes;
  }

  // Obtenir les items d'une commande
  Future<List<CommandeItem>> obtenirItemsCommande(int commandeId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'commande_items',
      where: 'commandeId = ?',
      whereArgs: [commandeId],
      orderBy: 'nomProduit ASC',
    );

    return List.generate(maps.length, (i) {
      return CommandeItem.fromMap(maps[i]);
    });
  }

  // Mettre à jour le statut d'une commande
  Future<int> mettreAJourStatut(
    int commandeId,
    StatutCommande nouveauStatut,
  ) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'commandes',
      {'statut': nouveauStatut.index},
      where: 'id = ?',
      whereArgs: [commandeId],
    );
  }

  // Obtenir les commandes par statut
  Future<List<Commande>> obtenirCommandesParStatut(
    StatutCommande statut,
  ) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'commandes',
      where: 'statut = ?',
      whereArgs: [statut.index],
      orderBy: 'dateCommande DESC',
    );

    List<Commande> commandes = [];
    for (final map in maps) {
      final items = await obtenirItemsCommande(map['id']);
      commandes.add(Commande.fromMap(map, items: items));
    }

    return commandes;
  }

  // Annuler une commande et restaurer le stock
  Future<bool> annulerCommande(int commandeId) async {
    final db = await _databaseHelper.database;

    return await db.transaction((txn) async {
      // Obtenir les items de la commande
      final List<Map<String, dynamic>> itemMaps = await txn.query(
        'commande_items',
        where: 'commandeId = ?',
        whereArgs: [commandeId],
      );

      // Restaurer le stock pour chaque item
      for (final itemMap in itemMaps) {
        final produitId = itemMap['produitId'] as int;
        final quantite = itemMap['quantite'] as int;

        await _restaurerStockDansTransaction(txn, produitId, quantite);
      }

      // Mettre à jour le statut de la commande
      await txn.update(
        'commandes',
        {'statut': StatutCommande.annulee.index},
        where: 'id = ?',
        whereArgs: [commandeId],
      );

      return true;
    });
  }

  // Restaurer le stock dans une transaction
  Future<void> _restaurerStockDansTransaction(
    Transaction txn,
    int produitId,
    int quantite,
  ) async {
    final List<Map<String, dynamic>> produitMaps = await txn.query(
      'produits',
      where: 'id = ?',
      whereArgs: [produitId],
    );

    if (produitMaps.isNotEmpty) {
      final stockActuel = produitMaps.first['stock'] as int;
      final nouveauStock = stockActuel + quantite;

      await txn.update(
        'produits',
        {'stock': nouveauStock},
        where: 'id = ?',
        whereArgs: [produitId],
      );
    }
  }

  // Obtenir le nombre total de commandes
  Future<int> obtenirNombreCommandes() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM commandes');
    return result.first['count'] as int;
  }

  // Obtenir le chiffre d'affaires total
  Future<double> obtenirChiffreAffaires() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT SUM(montantTotal) as total FROM commandes WHERE statut != ?',
      [StatutCommande.annulee.index],
    );

    final total = result.first['total'];
    return total != null ? (total as num).toDouble() : 0.0;
  }

  // Obtenir les commandes récentes (derniers 30 jours)
  Future<List<Commande>> obtenirCommandesRecentes() async {
    final db = await _databaseHelper.database;
    final dateLimit = DateTime.now().subtract(const Duration(days: 30));

    final List<Map<String, dynamic>> maps = await db.query(
      'commandes',
      where: 'dateCommande >= ?',
      whereArgs: [dateLimit.toIso8601String()],
      orderBy: 'dateCommande DESC',
    );

    List<Commande> commandes = [];
    for (final map in maps) {
      final items = await obtenirItemsCommande(map['id']);
      commandes.add(Commande.fromMap(map, items: items));
    }

    return commandes;
  }
}
