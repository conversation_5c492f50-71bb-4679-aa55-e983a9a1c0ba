import '../models/commande.dart';
import '../models/commande_item.dart';
import 'mock_data_service.dart';

class CommandeService {
  final MockDataService _mockService = MockDataService();

  // Créer une nouvelle commande avec ses items
  Future<int> creerCommande(Commande commande) async {
    await _mockService.initialize();
    return _mockService.addCommande(commande);
  }

  // Obtenir toutes les commandes
  Future<List<Commande>> obtenirToutesLesCommandes() async {
    await _mockService.initialize();
    final commandes = _mockService.commandes;
    commandes.sort((a, b) => b.dateCommande.compareTo(a.dateCommande));
    return commandes;
  }

  // Obtenir une commande par ID
  Future<Commande?> obtenirCommandeParId(int id) async {
    await _mockService.initialize();
    return _mockService.getCommandeById(id);
  }

  // Obtenir les commandes d'un client
  Future<List<Commande>> obtenirCommandesClient(int clientId) async {
    await _mockService.initialize();
    final commandes = _mockService.getCommandesByClient(clientId);
    commandes.sort((a, b) => b.dateCommande.compareTo(a.dateCommande));
    return commandes;
  }

  // Obtenir les items d'une commande
  Future<List<CommandeItem>> obtenirItemsCommande(int commandeId) async {
    await _mockService.initialize();
    final commande = _mockService.getCommandeById(commandeId);
    return commande?.items ?? [];
  }

  // Mettre à jour le statut d'une commande
  Future<int> mettreAJourStatut(
    int commandeId,
    StatutCommande nouveauStatut,
  ) async {
    await _mockService.initialize();
    return _mockService.updateCommandeStatut(commandeId, nouveauStatut) ? 1 : 0;
  }

  // Obtenir les commandes par statut
  Future<List<Commande>> obtenirCommandesParStatut(
    StatutCommande statut,
  ) async {
    await _mockService.initialize();
    final commandes =
        _mockService.commandes.where((c) => c.statut == statut).toList();
    commandes.sort((a, b) => b.dateCommande.compareTo(a.dateCommande));
    return commandes;
  }

  // Annuler une commande et restaurer le stock
  Future<bool> annulerCommande(int commandeId) async {
    await _mockService.initialize();

    final commande = _mockService.getCommandeById(commandeId);
    if (commande == null) return false;

    // Restaurer le stock pour chaque item
    for (final item in commande.items) {
      final produit = _mockService.getProduitById(item.produitId);
      if (produit != null) {
        final produitMisAJour = produit.copyWith(
          stock: produit.stock + item.quantite,
        );
        _mockService.updateProduit(produitMisAJour);
      }
    }

    // Mettre à jour le statut de la commande
    return _mockService.updateCommandeStatut(
      commandeId,
      StatutCommande.annulee,
    );
  }

  // Obtenir le nombre total de commandes
  Future<int> obtenirNombreCommandes() async {
    await _mockService.initialize();
    return _mockService.commandes.length;
  }

  // Obtenir le chiffre d'affaires total
  Future<double> obtenirChiffreAffaires() async {
    await _mockService.initialize();
    return _mockService.commandes
        .where((c) => c.statut != StatutCommande.annulee)
        .fold<double>(0.0, (total, commande) => total + commande.montantTotal);
  }

  // Obtenir les commandes récentes (derniers 30 jours)
  Future<List<Commande>> obtenirCommandesRecentes() async {
    await _mockService.initialize();
    final dateLimit = DateTime.now().subtract(const Duration(days: 30));

    final commandes =
        _mockService.commandes
            .where((c) => c.dateCommande.isAfter(dateLimit))
            .toList();
    commandes.sort((a, b) => b.dateCommande.compareTo(a.dateCommande));
    return commandes;
  }
}
