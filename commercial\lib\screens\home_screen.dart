import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/client_provider.dart';
import '../providers/produit_provider.dart';
import '../providers/commande_provider.dart';
import 'clients/clients_screen.dart';
import 'produits/produits_screen.dart';
import 'commandes/commandes_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  
  final List<Widget> _screens = [
    const DashboardTab(),
    const ClientsScreen(),
    const ProduitsScreen(),
    const CommandesScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _chargerDonnees();
  }

  Future<void> _chargerDonnees() async {
    final clientProvider = Provider.of<ClientProvider>(context, listen: false);
    final produitProvider = Provider.of<ProduitProvider>(context, listen: false);
    final commandeProvider = Provider.of<CommandeProvider>(context, listen: false);

    await Future.wait([
      clientProvider.chargerClients(),
      produitProvider.chargerProduits(),
      produitProvider.chargerCategories(),
      commandeProvider.chargerCommandes(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Tableau de bord',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: 'Clients',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory),
            label: 'Produits',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart),
            label: 'Commandes',
          ),
        ],
      ),
    );
  }
}

class DashboardTab extends StatelessWidget {
  const DashboardTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tableau de bord'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Bienvenue dans votre application de gestion commerciale',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 24),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildStatCard(
                    context,
                    'Clients',
                    Icons.people,
                    Colors.blue,
                    Consumer<ClientProvider>(
                      builder: (context, provider, child) {
                        return FutureBuilder<Map<String, dynamic>>(
                          future: provider.obtenirStatistiques(),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return Text(
                                '${snapshot.data!['nombreTotal']}',
                                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              );
                            }
                            return const CircularProgressIndicator(color: Colors.white);
                          },
                        );
                      },
                    ),
                  ),
                  _buildStatCard(
                    context,
                    'Produits',
                    Icons.inventory,
                    Colors.green,
                    Consumer<ProduitProvider>(
                      builder: (context, provider, child) {
                        return FutureBuilder<Map<String, dynamic>>(
                          future: provider.obtenirStatistiques(),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return Text(
                                '${snapshot.data!['nombreTotal']}',
                                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              );
                            }
                            return const CircularProgressIndicator(color: Colors.white);
                          },
                        );
                      },
                    ),
                  ),
                  _buildStatCard(
                    context,
                    'Commandes',
                    Icons.shopping_cart,
                    Colors.orange,
                    Consumer<CommandeProvider>(
                      builder: (context, provider, child) {
                        return FutureBuilder<Map<String, dynamic>>(
                          future: provider.obtenirStatistiques(),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return Text(
                                '${snapshot.data!['nombreTotal']}',
                                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              );
                            }
                            return const CircularProgressIndicator(color: Colors.white);
                          },
                        );
                      },
                    ),
                  ),
                  _buildStatCard(
                    context,
                    'Chiffre d\'affaires',
                    Icons.euro,
                    Colors.purple,
                    Consumer<CommandeProvider>(
                      builder: (context, provider, child) {
                        return FutureBuilder<Map<String, dynamic>>(
                          future: provider.obtenirStatistiques(),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              final ca = snapshot.data!['chiffreAffaires'] as double;
                              return Text(
                                '${ca.toStringAsFixed(0)}€',
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              );
                            }
                            return const CircularProgressIndicator(color: Colors.white);
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(BuildContext context, String title, IconData icon, Color color, Widget valueWidget) {
    return Card(
      elevation: 4,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [color, color.withOpacity(0.7)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 48,
              color: Colors.white,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            valueWidget,
          ],
        ),
      ),
    );
  }
}
