import 'commande_item.dart';

enum StatutCommande {
  enAttente,
  confirmee,
  enPreparation,
  expediee,
  livree,
  annulee,
}

class Commande {
  final int? id;
  final int clientId;
  final DateTime dateCommande;
  final StatutCommande statut;
  final double montantTotal;
  final String? notes;
  final List<CommandeItem> items;

  Commande({
    this.id,
    required this.clientId,
    required this.dateCommande,
    this.statut = StatutCommande.enAttente,
    required this.montantTotal,
    this.notes,
    this.items = const [],
  });

  // Convertir une Commande en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'clientId': clientId,
      'dateCommande': dateCommande.toIso8601String(),
      'statut': statut.index,
      'montantTotal': montantTotal,
      'notes': notes,
    };
  }

  // Créer une Commande à partir d'une Map de la base de données
  factory Commande.fromMap(Map<String, dynamic> map, {List<CommandeItem>? items}) {
    return Commande(
      id: map['id'],
      clientId: map['clientId'],
      dateCommande: DateTime.parse(map['dateCommande']),
      statut: StatutCommande.values[map['statut']],
      montantTotal: map['montantTotal'].toDouble(),
      notes: map['notes'],
      items: items ?? [],
    );
  }

  // Créer une copie de la commande avec des modifications
  Commande copyWith({
    int? id,
    int? clientId,
    DateTime? dateCommande,
    StatutCommande? statut,
    double? montantTotal,
    String? notes,
    List<CommandeItem>? items,
  }) {
    return Commande(
      id: id ?? this.id,
      clientId: clientId ?? this.clientId,
      dateCommande: dateCommande ?? this.dateCommande,
      statut: statut ?? this.statut,
      montantTotal: montantTotal ?? this.montantTotal,
      notes: notes ?? this.notes,
      items: items ?? this.items,
    );
  }

  // Calculer le montant total à partir des items
  double calculerMontantTotal() {
    return items.fold(0.0, (total, item) => total + item.sousTotal);
  }

  // Nombre total d'articles
  int get nombreArticles => items.fold(0, (total, item) => total + item.quantite);

  // Statut formaté
  String get statutFormate {
    switch (statut) {
      case StatutCommande.enAttente:
        return 'En attente';
      case StatutCommande.confirmee:
        return 'Confirmée';
      case StatutCommande.enPreparation:
        return 'En préparation';
      case StatutCommande.expediee:
        return 'Expédiée';
      case StatutCommande.livree:
        return 'Livrée';
      case StatutCommande.annulee:
        return 'Annulée';
    }
  }

  // Montant formaté
  String get montantFormate => '${montantTotal.toStringAsFixed(2)} €';

  @override
  String toString() {
    return 'Commande{id: $id, clientId: $clientId, statut: $statut, montant: $montantTotal}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Commande && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
