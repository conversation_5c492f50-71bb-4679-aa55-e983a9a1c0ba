class CommandeItem {
  final int? id;
  final int commandeId;
  final int produitId;
  final String nomProduit;
  final double prixUnitaire;
  final int quantite;
  final double sousTotal;

  CommandeItem({
    this.id,
    required this.commandeId,
    required this.produitId,
    required this.nomProduit,
    required this.prixUnitaire,
    required this.quantite,
    required this.sousTotal,
  });

  // Constructeur pour créer un item à partir d'un produit
  CommandeItem.fromProduit({
    this.id,
    required this.commandeId,
    required this.produitId,
    required this.nomProduit,
    required this.prixUnitaire,
    required this.quantite,
  }) : sousTotal = prixUnitaire * quantite;

  // Convertir un CommandeItem en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'commandeId': commandeId,
      'produitId': produitId,
      'nomProduit': nomProduit,
      'prixUnitaire': prixUnitaire,
      'quantite': quantite,
      'sousTotal': sousTotal,
    };
  }

  // Créer un CommandeItem à partir d'une Map de la base de données
  factory CommandeItem.fromMap(Map<String, dynamic> map) {
    return CommandeItem(
      id: map['id'],
      commandeId: map['commandeId'],
      produitId: map['produitId'],
      nomProduit: map['nomProduit'],
      prixUnitaire: map['prixUnitaire'].toDouble(),
      quantite: map['quantite'],
      sousTotal: map['sousTotal'].toDouble(),
    );
  }

  // Créer une copie de l'item avec des modifications
  CommandeItem copyWith({
    int? id,
    int? commandeId,
    int? produitId,
    String? nomProduit,
    double? prixUnitaire,
    int? quantite,
    double? sousTotal,
  }) {
    return CommandeItem(
      id: id ?? this.id,
      commandeId: commandeId ?? this.commandeId,
      produitId: produitId ?? this.produitId,
      nomProduit: nomProduit ?? this.nomProduit,
      prixUnitaire: prixUnitaire ?? this.prixUnitaire,
      quantite: quantite ?? this.quantite,
      sousTotal: sousTotal ?? this.sousTotal,
    );
  }

  // Prix unitaire formaté
  String get prixUnitaireFormate => '${prixUnitaire.toStringAsFixed(2)} €';

  // Sous-total formaté
  String get sousTotalFormate => '${sousTotal.toStringAsFixed(2)} €';

  @override
  String toString() {
    return 'CommandeItem{produit: $nomProduit, quantite: $quantite, sousTotal: $sousTotal}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommandeItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
