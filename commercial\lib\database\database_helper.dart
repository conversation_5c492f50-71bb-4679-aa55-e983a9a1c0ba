import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/client.dart';
import '../models/produit.dart';
import '../models/commande.dart';
import '../models/commande_item.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'commercial.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Table des clients
    await db.execute('''
      CREATE TABLE clients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        prenom TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        telephone TEXT NOT NULL,
        adresse TEXT NOT NULL,
        dateCreation TEXT NOT NULL
      )
    ''');

    // Table des produits
    await db.execute('''
      CREATE TABLE produits (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        description TEXT NOT NULL,
        prix REAL NOT NULL,
        stock INTEGER NOT NULL,
        imageUrl TEXT,
        categorie TEXT NOT NULL,
        dateCreation TEXT NOT NULL,
        actif INTEGER NOT NULL DEFAULT 1
      )
    ''');

    // Table des commandes
    await db.execute('''
      CREATE TABLE commandes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        clientId INTEGER NOT NULL,
        dateCommande TEXT NOT NULL,
        statut INTEGER NOT NULL,
        montantTotal REAL NOT NULL,
        notes TEXT,
        FOREIGN KEY (clientId) REFERENCES clients (id)
      )
    ''');

    // Table des items de commande
    await db.execute('''
      CREATE TABLE commande_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        commandeId INTEGER NOT NULL,
        produitId INTEGER NOT NULL,
        nomProduit TEXT NOT NULL,
        prixUnitaire REAL NOT NULL,
        quantite INTEGER NOT NULL,
        sousTotal REAL NOT NULL,
        FOREIGN KEY (commandeId) REFERENCES commandes (id),
        FOREIGN KEY (produitId) REFERENCES produits (id)
      )
    ''');

    // Insérer des données de test
    await _insertSampleData(db);
  }

  Future<void> _insertSampleData(Database db) async {
    // Clients de test
    await db.insert('clients', {
      'nom': 'Dupont',
      'prenom': 'Jean',
      'email': '<EMAIL>',
      'telephone': '0123456789',
      'adresse': '123 Rue de la Paix, 75001 Paris',
      'dateCreation': DateTime.now().toIso8601String(),
    });

    await db.insert('clients', {
      'nom': 'Martin',
      'prenom': 'Marie',
      'email': '<EMAIL>',
      'telephone': '0987654321',
      'adresse': '456 Avenue des Champs, 69000 Lyon',
      'dateCreation': DateTime.now().toIso8601String(),
    });

    // Produits de test
    await db.insert('produits', {
      'nom': 'Smartphone Galaxy',
      'description': 'Smartphone Android dernière génération',
      'prix': 599.99,
      'stock': 25,
      'categorie': 'Électronique',
      'dateCreation': DateTime.now().toIso8601String(),
      'actif': 1,
    });

    await db.insert('produits', {
      'nom': 'Ordinateur Portable',
      'description': 'PC portable 15 pouces, 8GB RAM, SSD 256GB',
      'prix': 899.99,
      'stock': 15,
      'categorie': 'Informatique',
      'dateCreation': DateTime.now().toIso8601String(),
      'actif': 1,
    });

    await db.insert('produits', {
      'nom': 'Casque Audio',
      'description': 'Casque sans fil avec réduction de bruit',
      'prix': 199.99,
      'stock': 50,
      'categorie': 'Audio',
      'dateCreation': DateTime.now().toIso8601String(),
      'actif': 1,
    });
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }

  Future<void> deleteDatabase() async {
    String path = join(await getDatabasesPath(), 'commercial.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
