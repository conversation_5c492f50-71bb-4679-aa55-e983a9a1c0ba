import '../models/client.dart';
import '../models/produit.dart';
import '../models/commande.dart';
import '../models/commande_item.dart';

class MockDataService {
  static final MockDataService _instance = MockDataService._internal();
  factory MockDataService() => _instance;
  MockDataService._internal();

  // Données en mémoire
  final List<Client> _clients = [];
  final List<Produit> _produits = [];
  final List<Commande> _commandes = [];
  final List<CommandeItem> _commandeItems = [];

  int _nextClientId = 1;
  int _nextProduitId = 1;
  int _nextCommandeId = 1;
  int _nextCommandeItemId = 1;

  bool _initialized = false;

  Future<void> initialize() async {
    if (_initialized) return;

    // Données de test
    await _insertSampleData();
    _initialized = true;
  }

  Future<void> _insertSampleData() async {
    // Clients de test
    _clients.addAll([
      Client(
        id: _nextClientId++,
        nom: '<PERSON><PERSON>',
        prenom: '<PERSON>',
        email: '<EMAIL>',
        telephone: '0123456789',
        adresse: '123 Rue de la Paix, 75001 Paris',
        dateCreation: DateTime.now().subtract(const Duration(days: 30)),
      ),
      Client(
        id: _nextClientId++,
        nom: 'Martin',
        prenom: 'Marie',
        email: '<EMAIL>',
        telephone: '0987654321',
        adresse: '456 Avenue des Champs, 69000 Lyon',
        dateCreation: DateTime.now().subtract(const Duration(days: 15)),
      ),
      Client(
        id: _nextClientId++,
        nom: 'Bernard',
        prenom: 'Pierre',
        email: '<EMAIL>',
        telephone: '0147258369',
        adresse: '789 Boulevard Saint-Germain, 75006 Paris',
        dateCreation: DateTime.now().subtract(const Duration(days: 7)),
      ),
    ]);

    // Produits de test
    _produits.addAll([
      Produit(
        id: _nextProduitId++,
        nom: 'Smartphone Galaxy',
        description: 'Smartphone Android dernière génération avec écran OLED',
        prix: 599.99,
        stock: 25,
        categorie: 'Électronique',
        dateCreation: DateTime.now().subtract(const Duration(days: 60)),
      ),
      Produit(
        id: _nextProduitId++,
        nom: 'Ordinateur Portable',
        description: 'PC portable 15 pouces, 8GB RAM, SSD 256GB',
        prix: 899.99,
        stock: 15,
        categorie: 'Informatique',
        dateCreation: DateTime.now().subtract(const Duration(days: 45)),
      ),
      Produit(
        id: _nextProduitId++,
        nom: 'Casque Audio',
        description: 'Casque sans fil avec réduction de bruit active',
        prix: 199.99,
        stock: 50,
        categorie: 'Audio',
        dateCreation: DateTime.now().subtract(const Duration(days: 30)),
      ),
      Produit(
        id: _nextProduitId++,
        nom: 'Tablette iPad',
        description: 'Tablette 10 pouces avec stylet inclus',
        prix: 449.99,
        stock: 8,
        categorie: 'Électronique',
        dateCreation: DateTime.now().subtract(const Duration(days: 20)),
      ),
      Produit(
        id: _nextProduitId++,
        nom: 'Souris Gaming',
        description: 'Souris gaming haute précision avec éclairage RGB',
        prix: 79.99,
        stock: 0,
        categorie: 'Gaming',
        dateCreation: DateTime.now().subtract(const Duration(days: 10)),
      ),
    ]);

    // Commandes de test
    final commande1Items = [
      CommandeItem.fromProduit(
        id: _nextCommandeItemId++,
        commandeId: _nextCommandeId,
        produitId: 1,
        nomProduit: 'Smartphone Galaxy',
        prixUnitaire: 599.99,
        quantite: 1,
      ),
      CommandeItem.fromProduit(
        id: _nextCommandeItemId++,
        commandeId: _nextCommandeId,
        produitId: 3,
        nomProduit: 'Casque Audio',
        prixUnitaire: 199.99,
        quantite: 2,
      ),
    ];

    _commandes.add(
      Commande(
        id: _nextCommandeId++,
        clientId: 1,
        dateCommande: DateTime.now().subtract(const Duration(days: 5)),
        statut: StatutCommande.livree,
        montantTotal: 999.97,
        items: commande1Items,
      ),
    );

    _commandeItems.addAll(commande1Items);

    final commande2Items = [
      CommandeItem.fromProduit(
        id: _nextCommandeItemId++,
        commandeId: _nextCommandeId,
        produitId: 2,
        nomProduit: 'Ordinateur Portable',
        prixUnitaire: 899.99,
        quantite: 1,
      ),
    ];

    _commandes.add(
      Commande(
        id: _nextCommandeId++,
        clientId: 2,
        dateCommande: DateTime.now().subtract(const Duration(days: 2)),
        statut: StatutCommande.enPreparation,
        montantTotal: 899.99,
        items: commande2Items,
      ),
    );

    _commandeItems.addAll(commande2Items);
  }

  // Méthodes pour les clients
  List<Client> get clients => List.from(_clients);

  Client? getClientById(int id) {
    try {
      return _clients.firstWhere((c) => c.id == id);
    } catch (e) {
      return null;
    }
  }

  int addClient(Client client) {
    final newClient = client.copyWith(id: _nextClientId++);
    _clients.add(newClient);
    return newClient.id!;
  }

  bool updateClient(Client client) {
    final index = _clients.indexWhere((c) => c.id == client.id);
    if (index != -1) {
      _clients[index] = client;
      return true;
    }
    return false;
  }

  bool deleteClient(int id) {
    final initialLength = _clients.length;
    _clients.removeWhere((c) => c.id == id);
    return _clients.length < initialLength;
  }

  List<Client> searchClients(String term) {
    final lowerTerm = term.toLowerCase();
    return _clients
        .where(
          (c) =>
              c.nom.toLowerCase().contains(lowerTerm) ||
              c.prenom.toLowerCase().contains(lowerTerm) ||
              c.email.toLowerCase().contains(lowerTerm),
        )
        .toList();
  }

  bool emailExists(String email, {int? excludeId}) {
    return _clients.any(
      (c) =>
          c.email.toLowerCase() == email.toLowerCase() &&
          (excludeId == null || c.id != excludeId),
    );
  }

  // Méthodes pour les produits
  List<Produit> get produits => List.from(_produits);

  Produit? getProduitById(int id) {
    try {
      return _produits.firstWhere((p) => p.id == id);
    } catch (e) {
      return null;
    }
  }

  int addProduit(Produit produit) {
    final newProduit = produit.copyWith(id: _nextProduitId++);
    _produits.add(newProduit);
    return newProduit.id!;
  }

  bool updateProduit(Produit produit) {
    final index = _produits.indexWhere((p) => p.id == produit.id);
    if (index != -1) {
      _produits[index] = produit;
      return true;
    }
    return false;
  }

  bool deleteProduit(int id) {
    final index = _produits.indexWhere((p) => p.id == id);
    if (index != -1) {
      _produits[index] = _produits[index].copyWith(actif: false);
      return true;
    }
    return false;
  }

  List<Produit> searchProduits(String term) {
    final lowerTerm = term.toLowerCase();
    return _produits
        .where(
          (p) =>
              p.actif &&
              (p.nom.toLowerCase().contains(lowerTerm) ||
                  p.description.toLowerCase().contains(lowerTerm) ||
                  p.categorie.toLowerCase().contains(lowerTerm)),
        )
        .toList();
  }

  List<Produit> getProduitsByCategory(String category) {
    return _produits.where((p) => p.actif && p.categorie == category).toList();
  }

  List<String> get categories {
    return _produits
        .where((p) => p.actif)
        .map((p) => p.categorie)
        .toSet()
        .toList()
      ..sort();
  }

  // Méthodes pour les commandes
  List<Commande> get commandes => List.from(_commandes);

  Commande? getCommandeById(int id) {
    try {
      return _commandes.firstWhere((c) => c.id == id);
    } catch (e) {
      return null;
    }
  }

  List<Commande> getCommandesByClient(int clientId) {
    return _commandes.where((c) => c.clientId == clientId).toList();
  }

  int addCommande(Commande commande) {
    final commandeId = _nextCommandeId++;
    final newCommande = commande.copyWith(id: commandeId);

    // Ajouter les items avec l'ID de commande
    final newItems =
        commande.items
            .map(
              (item) => item.copyWith(
                id: _nextCommandeItemId++,
                commandeId: commandeId,
              ),
            )
            .toList();

    _commandeItems.addAll(newItems);
    _commandes.add(newCommande.copyWith(items: newItems));

    // Réduire le stock
    for (final item in newItems) {
      final produitIndex = _produits.indexWhere((p) => p.id == item.produitId);
      if (produitIndex != -1) {
        final produit = _produits[produitIndex];
        _produits[produitIndex] = produit.copyWith(
          stock: produit.stock - item.quantite,
        );
      }
    }

    return commandeId;
  }

  bool updateCommandeStatut(int commandeId, StatutCommande statut) {
    final index = _commandes.indexWhere((c) => c.id == commandeId);
    if (index != -1) {
      _commandes[index] = _commandes[index].copyWith(statut: statut);
      return true;
    }
    return false;
  }
}
