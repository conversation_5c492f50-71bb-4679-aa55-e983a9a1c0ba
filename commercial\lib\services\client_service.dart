import '../models/client.dart';
import 'mock_data_service.dart';

class ClientService {
  final MockDataService _mockService = MockDataService();

  // Créer un nouveau client
  Future<int> creerClient(Client client) async {
    await _mockService.initialize();
    return _mockService.addClient(client);
  }

  // Obtenir tous les clients
  Future<List<Client>> obtenirTousLesClients() async {
    await _mockService.initialize();
    final clients = _mockService.clients;
    clients.sort((a, b) => a.nom.compareTo(b.nom));
    return clients;
  }

  // Obtenir un client par ID
  Future<Client?> obtenirClientParId(int id) async {
    await _mockService.initialize();
    return _mockService.getClientById(id);
  }

  // Rechercher des clients par nom ou email
  Future<List<Client>> rechercherClients(String terme) async {
    await _mockService.initialize();
    final clients = _mockService.searchClients(terme);
    clients.sort((a, b) => a.nom.compareTo(b.nom));
    return clients;
  }

  // Mettre à jour un client
  Future<int> mettreAJourClient(Client client) async {
    await _mockService.initialize();
    return _mockService.updateClient(client) ? 1 : 0;
  }

  // Supprimer un client
  Future<int> supprimerClient(int id) async {
    await _mockService.initialize();

    // Vérifier s'il y a des commandes associées
    final commandes = _mockService.getCommandesByClient(id);

    if (commandes.isNotEmpty) {
      throw Exception(
        'Impossible de supprimer le client car il a des commandes associées',
      );
    }

    return _mockService.deleteClient(id) ? 1 : 0;
  }

  // Vérifier si un email existe déjà
  Future<bool> emailExiste(String email, {int? excludeId}) async {
    await _mockService.initialize();
    return _mockService.emailExists(email, excludeId: excludeId);
  }

  // Obtenir le nombre total de clients
  Future<int> obtenirNombreClients() async {
    await _mockService.initialize();
    return _mockService.clients.length;
  }

  // Obtenir les clients récents (derniers 30 jours)
  Future<List<Client>> obtenirClientsRecents() async {
    await _mockService.initialize();
    final dateLimit = DateTime.now().subtract(const Duration(days: 30));

    return _mockService.clients
        .where((client) => client.dateCreation.isAfter(dateLimit))
        .toList()
      ..sort((a, b) => b.dateCreation.compareTo(a.dateCreation));
  }
}
