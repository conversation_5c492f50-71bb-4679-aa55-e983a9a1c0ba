import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/client.dart';

class ClientService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // Créer un nouveau client
  Future<int> creerClient(Client client) async {
    final db = await _databaseHelper.database;
    return await db.insert('clients', client.toMap());
  }

  // Obtenir tous les clients
  Future<List<Client>> obtenirTousLesClients() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'clients',
      orderBy: 'nom ASC, prenom ASC',
    );

    return List.generate(maps.length, (i) {
      return Client.fromMap(maps[i]);
    });
  }

  // Obtenir un client par ID
  Future<Client?> obtenirClientParId(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'clients',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Client.fromMap(maps.first);
    }
    return null;
  }

  // Rechercher des clients par nom ou email
  Future<List<Client>> rechercherClients(String terme) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'clients',
      where: 'nom LIKE ? OR prenom LIKE ? OR email LIKE ?',
      whereArgs: ['%$terme%', '%$terme%', '%$terme%'],
      orderBy: 'nom ASC, prenom ASC',
    );

    return List.generate(maps.length, (i) {
      return Client.fromMap(maps[i]);
    });
  }

  // Mettre à jour un client
  Future<int> mettreAJourClient(Client client) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'clients',
      client.toMap(),
      where: 'id = ?',
      whereArgs: [client.id],
    );
  }

  // Supprimer un client
  Future<int> supprimerClient(int id) async {
    final db = await _databaseHelper.database;
    
    // Vérifier s'il y a des commandes associées
    final commandes = await db.query(
      'commandes',
      where: 'clientId = ?',
      whereArgs: [id],
    );
    
    if (commandes.isNotEmpty) {
      throw Exception('Impossible de supprimer le client car il a des commandes associées');
    }
    
    return await db.delete(
      'clients',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Vérifier si un email existe déjà
  Future<bool> emailExiste(String email, {int? excludeId}) async {
    final db = await _databaseHelper.database;
    String whereClause = 'email = ?';
    List<dynamic> whereArgs = [email];
    
    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }
    
    final List<Map<String, dynamic>> maps = await db.query(
      'clients',
      where: whereClause,
      whereArgs: whereArgs,
    );
    
    return maps.isNotEmpty;
  }

  // Obtenir le nombre total de clients
  Future<int> obtenirNombreClients() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM clients');
    return result.first['count'] as int;
  }

  // Obtenir les clients récents (derniers 30 jours)
  Future<List<Client>> obtenirClientsRecents() async {
    final db = await _databaseHelper.database;
    final dateLimit = DateTime.now().subtract(const Duration(days: 30));
    
    final List<Map<String, dynamic>> maps = await db.query(
      'clients',
      where: 'dateCreation >= ?',
      whereArgs: [dateLimit.toIso8601String()],
      orderBy: 'dateCreation DESC',
    );

    return List.generate(maps.length, (i) {
      return Client.fromMap(maps[i]);
    });
  }
}
