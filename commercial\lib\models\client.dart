class Client {
  final int? id;
  final String nom;
  final String prenom;
  final String email;
  final String telephone;
  final String adresse;
  final DateTime dateCreation;

  Client({
    this.id,
    required this.nom,
    required this.prenom,
    required this.email,
    required this.telephone,
    required this.adresse,
    required this.dateCreation,
  });

  // Convertir un Client en Map pour la base de données
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nom': nom,
      'prenom': prenom,
      'email': email,
      'telephone': telephone,
      'adresse': adresse,
      'dateCreation': dateCreation.toIso8601String(),
    };
  }

  // Créer un Client à partir d'une Map de la base de données
  factory Client.fromMap(Map<String, dynamic> map) {
    return Client(
      id: map['id'],
      nom: map['nom'],
      prenom: map['prenom'],
      email: map['email'],
      telephone: map['telephone'],
      adresse: map['adresse'],
      dateCreation: DateTime.parse(map['dateCreation']),
    );
  }

  // Créer une copie du client avec des modifications
  Client copyWith({
    int? id,
    String? nom,
    String? prenom,
    String? email,
    String? telephone,
    String? adresse,
    DateTime? dateCreation,
  }) {
    return Client(
      id: id ?? this.id,
      nom: nom ?? this.nom,
      prenom: prenom ?? this.prenom,
      email: email ?? this.email,
      telephone: telephone ?? this.telephone,
      adresse: adresse ?? this.adresse,
      dateCreation: dateCreation ?? this.dateCreation,
    );
  }

  // Nom complet du client
  String get nomComplet => '$prenom $nom';

  @override
  String toString() {
    return 'Client{id: $id, nom: $nom, prenom: $prenom, email: $email}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Client && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
