import 'package:flutter/foundation.dart';
import '../models/produit.dart';
import '../services/produit_service.dart';

class ProduitProvider with ChangeNotifier {
  final ProduitService _produitService = ProduitService();
  
  List<Produit> _produits = [];
  List<String> _categories = [];
  bool _isLoading = false;
  String? _error;

  List<Produit> get produits => _produits;
  List<String> get categories => _categories;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Charger tous les produits
  Future<void> chargerProduits() async {
    _setLoading(true);
    try {
      _produits = await _produitService.obtenirProduitsActifs();
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du chargement des produits: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Charger les catégories
  Future<void> chargerCategories() async {
    try {
      _categories = await _produitService.obtenirCategories();
      notifyListeners();
    } catch (e) {
      _error = 'Erreur lors du chargement des catégories: $e';
      notifyListeners();
    }
  }

  // Ajouter un nouveau produit
  Future<bool> ajouterProduit(Produit produit) async {
    try {
      final id = await _produitService.creerProduit(produit);
      final nouveauProduit = produit.copyWith(id: id);
      _produits.add(nouveauProduit);
      _produits.sort((a, b) => a.nom.compareTo(b.nom));
      
      // Mettre à jour les catégories si nécessaire
      if (!_categories.contains(produit.categorie)) {
        _categories.add(produit.categorie);
        _categories.sort();
      }
      
      _error = null;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de l\'ajout du produit: $e';
      notifyListeners();
      return false;
    }
  }

  // Modifier un produit
  Future<bool> modifierProduit(Produit produit) async {
    try {
      await _produitService.mettreAJourProduit(produit);
      final index = _produits.indexWhere((p) => p.id == produit.id);
      if (index != -1) {
        _produits[index] = produit;
        _produits.sort((a, b) => a.nom.compareTo(b.nom));
      }
      
      // Mettre à jour les catégories si nécessaire
      if (!_categories.contains(produit.categorie)) {
        _categories.add(produit.categorie);
        _categories.sort();
      }
      
      _error = null;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la modification du produit: $e';
      notifyListeners();
      return false;
    }
  }

  // Supprimer un produit (désactiver)
  Future<bool> supprimerProduit(int produitId) async {
    try {
      await _produitService.supprimerProduit(produitId);
      _produits.removeWhere((produit) => produit.id == produitId);
      _error = null;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la suppression du produit: $e';
      notifyListeners();
      return false;
    }
  }

  // Rechercher des produits
  Future<void> rechercherProduits(String terme) async {
    if (terme.isEmpty) {
      await chargerProduits();
      return;
    }

    _setLoading(true);
    try {
      _produits = await _produitService.rechercherProduits(terme);
      _error = null;
    } catch (e) {
      _error = 'Erreur lors de la recherche: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Filtrer par catégorie
  Future<void> filtrerParCategorie(String categorie) async {
    _setLoading(true);
    try {
      if (categorie.isEmpty || categorie == 'Toutes') {
        _produits = await _produitService.obtenirProduitsActifs();
      } else {
        _produits = await _produitService.obtenirProduitsParCategorie(categorie);
      }
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du filtrage: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Obtenir un produit par ID
  Produit? obtenirProduitParId(int id) {
    try {
      return _produits.firstWhere((produit) => produit.id == id);
    } catch (e) {
      return null;
    }
  }

  // Mettre à jour le stock
  Future<bool> mettreAJourStock(int produitId, int nouveauStock) async {
    try {
      await _produitService.mettreAJourStock(produitId, nouveauStock);
      final index = _produits.indexWhere((p) => p.id == produitId);
      if (index != -1) {
        _produits[index] = _produits[index].copyWith(stock: nouveauStock);
      }
      _error = null;
      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la mise à jour du stock: $e';
      notifyListeners();
      return false;
    }
  }

  // Obtenir les produits en rupture de stock
  Future<List<Produit>> obtenirProduitsRuptureStock() async {
    try {
      return await _produitService.obtenirProduitsRuptureStock();
    } catch (e) {
      return [];
    }
  }

  // Obtenir les produits avec stock faible
  Future<List<Produit>> obtenirProduitsStockFaible() async {
    try {
      return await _produitService.obtenirProduitsStockFaible();
    } catch (e) {
      return [];
    }
  }

  // Effacer l'erreur
  void effacerErreur() {
    _error = null;
    notifyListeners();
  }

  // Méthode privée pour gérer le loading
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Obtenir les statistiques des produits
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      final nombreTotal = await _produitService.obtenirNombreProduits();
      final ruptureStock = await _produitService.obtenirProduitsRuptureStock();
      final stockFaible = await _produitService.obtenirProduitsStockFaible();
      
      return {
        'nombreTotal': nombreTotal,
        'ruptureStock': ruptureStock.length,
        'stockFaible': stockFaible.length,
        'produitsRuptureStock': ruptureStock,
        'produitsStockFaible': stockFaible,
      };
    } catch (e) {
      return {
        'nombreTotal': 0,
        'ruptureStock': 0,
        'stockFaible': 0,
        'produitsRuptureStock': <Produit>[],
        'produitsStockFaible': <Produit>[],
      };
    }
  }
}
